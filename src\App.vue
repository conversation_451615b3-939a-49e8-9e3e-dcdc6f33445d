<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>步骤条与折叠面板演示</h1>
      <p>展示 Ant Design Vue 的 Steps 和 Collapse 组件使用</p>
    </div>

    <!-- 步骤条部分 -->
    <a-card title="步骤条演示" class="section-card">
      <div class="steps-section">
        <a-steps :current="currentStep" class="custom-steps">
          <a-step title="生成搜索查询" description="根据问题生成相关关键词">
            <template #icon>
              <SearchOutlined />
            </template>
          </a-step>
          <a-step title="网络研究" description="收集相关信息和资料">
            <template #icon>
              <GlobalOutlined />
            </template>
          </a-step>
          <a-step title="已理解您的问题" description="完成问题解析和信息整合">
            <template #icon>
              <CheckCircleOutlined />
            </template>
          </a-step>
          <a-step title="已经做好答案准备" description="本次回答参考了7个知识片段">
            <template #icon>
              <FileTextOutlined />
            </template>
          </a-step>
        </a-steps>

        <!-- 步骤控制按钮 -->
        <div class="steps-controls">
          <a-space>
            <a-button @click="prevStep" :disabled="currentStep === 0">
              上一步
            </a-button>
            <a-button type="primary" @click="nextStep" :disabled="currentStep === 3">
              下一步
            </a-button>
            <a-button @click="resetSteps">
              重置
            </a-button>
          </a-space>
        </div>

        <!-- 当前步骤内容 -->
        <div class="step-content">
          <a-alert
            :message="stepContents[currentStep].title"
            :description="stepContents[currentStep].description"
            :type="stepContents[currentStep].type"
            show-icon
          />
        </div>
      </div>
    </a-card>

    <!-- 折叠面板部分 -->
    <a-card title="折叠面板演示" class="section-card">
      <a-collapse v-model:activeKey="activeKeys" class="custom-collapse">
        <a-collapse-panel key="1" header="生成搜索查询">
          <template #extra>
            <a-tag color="blue">已完成</a-tag>
          </template>
          <div class="collapse-content">
            <p>根据问题生成相关关键词</p>
            <a-descriptions :column="1" bordered size="small">
              <a-descriptions-item label="状态">已完成</a-descriptions-item>
              <a-descriptions-item label="关键词">政府采购, 框架协议, 管理办法</a-descriptions-item>
              <a-descriptions-item label="完成时间">2024-01-15 10:30:00</a-descriptions-item>
            </a-descriptions>
          </div>
        </a-collapse-panel>

        <a-collapse-panel key="2" header="网络研究">
          <template #extra>
            <a-tag color="green">42个来源</a-tag>
          </template>
          <div class="collapse-content">
            <p>收集相关信息和资料</p>
            <a-list
              size="small"
              :data-source="researchSources"
              :split="false"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <a :href="item.url" target="_blank">{{ item.title }}</a>
                    </template>
                    <template #description>
                      {{ item.description }}
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </div>
        </a-collapse-panel>

        <a-collapse-panel key="3" header="已理解您的问题">
          <template #extra>
            <a-tag color="orange">进行中</a-tag>
          </template>
          <div class="collapse-content">
            <p>完成问题解析和信息整合</p>
            <a-progress :percent="85" status="active" />
            <div class="progress-info">
              <p>正在分析问题关键点...</p>
              <ul>
                <li>✓ 识别问题类型</li>
                <li>✓ 提取关键信息</li>
                <li>✓ 匹配相关资料</li>
                <li>⏳ 生成回答框架</li>
              </ul>
            </div>
          </div>
        </a-collapse-panel>

        <a-collapse-panel key="4" header="已经做好答案准备">
          <div class="collapse-content">
            <p>本次回答参考了7个知识片段</p>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-statistic title="参考文档" :value="7" suffix="个" />
              </a-col>
              <a-col :span="12">
                <a-statistic title="置信度" :value="92.5" suffix="%" />
              </a-col>
            </a-row>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </a-card>

    <!-- 原有表单部分 -->
    <a-card title="表单配置" class="section-card">
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        layout="vertical"
        @finish="onFinish"
        @finishFailed="onFinishFailed"
        class="custom-form"
      >
        <!-- 文件名称 -->
        <a-form-item
          label="文件名称"
          name="fileName"
          :rules="rules.fileName"
        >
          <a-input
            v-model:value="formState.fileName"
            placeholder="请输入文件名称"
            size="large"
            show-count
            :maxlength="100"
          />
        </a-form-item>

        <!-- 开启状态 -->
        <a-form-item
          label="开启状态"
          name="isEnabled"
          :rules="rules.isEnabled"
        >
          <a-switch
            v-model:checked="formState.isEnabled"
            checked-children="开启"
            un-checked-children="关闭"
          />
          <span class="switch-description">
            {{ formState.isEnabled ? '当前状态：开启' : '当前状态：关闭' }}
          </span>
        </a-form-item>

        <!-- 标签 -->
        <a-form-item
          label="标签"
          name="tags"
          :rules="rules.tags"
        >
          <div class="tags-container">
            <!-- 现有标签 -->
            <a-tag
              v-for="(tag, index) in formState.tags"
              :key="index"
              closable
              @close="removeTag(index)"
              class="custom-tag"
            >
              {{ tag }}
            </a-tag>

            <!-- 新标签输入框 -->
            <a-input
              v-if="newTagVisible"
              v-model:value="newTagValue"
              @pressEnter="addNewTag"
              @blur="addNewTag"
              class="tag-input"
              placeholder="输入标签名"
              size="small"
            />

            <!-- 新增标签按钮 -->
            <a-tag
              v-else
              @click="showNewTagInput"
              class="add-tag"
            >
              <PlusOutlined />
              新标签
            </a-tag>
          </div>
        </a-form-item>

        <!-- 操作按钮 -->
        <a-form-item class="form-actions">
          <a-space>
            <a-button type="primary" html-type="submit" size="large">
              提交
            </a-button>
            <a-button @click="resetForm" size="large">
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  PlusOutlined,
  SearchOutlined,
  GlobalOutlined,
  CheckCircleOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'

// 表单引用
const formRef = ref<FormInstance>()

// 步骤条相关状态
const currentStep = ref(0)

// 步骤内容配置
const stepContents = [
  {
    title: '正在生成搜索查询',
    description: '根据您的问题生成相关关键词，提取核心概念进行搜索',
    type: 'info'
  },
  {
    title: '网络研究进行中',
    description: '正在从42个来源收集相关信息和资料，包括官方文档、法规条文等',
    type: 'warning'
  },
  {
    title: '问题理解完成',
    description: '已完成问题解析和信息整合，准确理解您的需求',
    type: 'success'
  },
  {
    title: '答案准备就绪',
    description: '基于7个知识片段生成了完整的回答，置信度达到92.5%',
    type: 'success'
  }
]

// 折叠面板活跃键
const activeKeys = ref(['1', '2'])

// 研究来源数据
const researchSources = [
  {
    title: '政府采购框架协议采购方式管理暂行办法',
    description: '财政部关于政府采购框架协议的最新管理规定',
    url: '#'
  },
  {
    title: '政府采购法实施条例',
    description: '国务院关于政府采购法的具体实施细则',
    url: '#'
  },
  {
    title: '政府采购货物和服务招标投标管理办法',
    description: '财政部制定的招标投标具体操作规范',
    url: '#'
  }
]

// 步骤控制方法
const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++
    message.success(`进入第${currentStep.value + 1}步`)
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
    message.info(`返回第${currentStep.value + 1}步`)
  }
}

const resetSteps = () => {
  currentStep.value = 0
  message.info('步骤已重置')
}

// 表单数据
const formState = reactive({
  fileName: '政府采购框架协议采购方式管理暂行办法',
  isEnabled: true,
  tags: ['标签1', '标签2', '标签3']
})

// 表单验证规则
const rules = {
  fileName: [
    { required: true, message: '请输入文件名称', trigger: 'blur' },
    { min: 2, max: 100, message: '文件名称长度应在2-100个字符之间', trigger: 'blur' }
  ],
  isEnabled: [
    { required: true, message: '请选择开启状态', trigger: 'change' }
  ],
  tags: [
    { required: true, message: '请至少添加一个标签', trigger: 'change' },
    { type: 'array', min: 1, message: '请至少添加一个标签', trigger: 'change' }
  ]
}

// 标签相关状态
const newTagVisible = ref(false)
const newTagValue = ref('')

// 删除标签
const removeTag = (index: number) => {
  formState.tags.splice(index, 1)
}

// 显示新标签输入框
const showNewTagInput = () => {
  newTagVisible.value = true
}

// 添加新标签
const addNewTag = () => {
  if (newTagValue.value && !formState.tags.includes(newTagValue.value)) {
    formState.tags.push(newTagValue.value)
    message.success('标签添加成功')
  } else if (formState.tags.includes(newTagValue.value)) {
    message.warning('标签已存在')
  }
  newTagVisible.value = false
  newTagValue.value = ''
}

// 表单提交
const onFinish = (values: any) => {
  console.log('表单数据:', values)
  message.success('表单提交成功！')
}

// 表单提交失败
const onFinishFailed = (errorInfo: any) => {
  console.log('表单验证失败:', errorInfo)
  message.error('请检查表单信息')
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  formState.tags = []
  message.info('表单已重置')
}

</script>

<style scoped>
/* 页面整体布局 */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.section-card {
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: none;
}

/* 步骤条样式 */
.steps-section {
  padding: 16px 0;
}

.custom-steps {
  margin-bottom: 32px;
}

.steps-controls {
  margin-bottom: 24px;
  text-align: center;
}

.step-content {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

/* 折叠面板样式 */
.custom-collapse {
  background: transparent;
}

.collapse-content {
  padding: 16px 0;
}

.progress-info {
  margin-top: 16px;
}

.progress-info ul {
  margin: 12px 0;
  padding-left: 20px;
}

.progress-info li {
  margin: 8px 0;
  font-size: 14px;
}

/* 原有表单样式 */
.custom-form {
  margin-top: 16px;
}

.switch-description {
  margin-left: 12px;
  color: #666;
  font-size: 14px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  min-height: 32px;
}

.custom-tag {
  margin: 0;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s;
}

.custom-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-input {
  width: 120px;
  margin: 0;
}

.add-tag {
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
  margin: 0;
}

.add-tag:hover {
  border-color: #1890ff;
  color: #1890ff;
  background: #f0f8ff;
}

.form-actions {
  margin-top: 32px;
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .page-header p {
    font-size: 14px;
  }

  .tags-container {
    gap: 6px;
  }

  .tag-input {
    width: 100px;
  }

  .section-card {
    margin-bottom: 16px;
  }
}

/* 深度样式调整 */
:deep(.ant-card-head-title) {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

:deep(.ant-steps-item-title) {
  font-weight: 500;
}

:deep(.ant-collapse-header) {
  font-weight: 500;
  font-size: 16px;
}

:deep(.ant-collapse-content-box) {
  padding: 16px 24px;
}

/* 表单项间距调整 */
:deep(.ant-form-item) {
  margin-bottom: 24px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
  color: #262626;
}

/* 输入框样式增强 */
:deep(.ant-input) {
  border-radius: 8px;
  transition: all 0.2s;
}

:deep(.ant-input:focus) {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 开关样式增强 */
:deep(.ant-switch) {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 按钮样式增强 */
:deep(.ant-btn) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s;
}

:deep(.ant-btn-primary) {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

:deep(.ant-btn-primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

/* 步骤条图标样式 */
:deep(.ant-steps-item-icon) {
  border-radius: 50%;
}

:deep(.ant-steps-item-process .ant-steps-item-icon) {
  background: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-steps-item-finish .ant-steps-item-icon) {
  background: #52c41a;
  border-color: #52c41a;
}

/* 统计数字样式 */
:deep(.ant-statistic-content) {
  font-weight: 600;
}

/* 进度条样式 */
:deep(.ant-progress-bg) {
  border-radius: 4px;
}
</style>
