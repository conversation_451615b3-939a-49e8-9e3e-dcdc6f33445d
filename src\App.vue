<template>
  <div class="form-container">
    <a-card title="表单配置" class="form-card">
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        layout="vertical"
        @finish="onFinish"
        @finishFailed="onFinishFailed"
        class="custom-form"
      >
        <!-- 文件名称 -->
        <a-form-item
          label="文件名称"
          name="fileName"
          :rules="rules.fileName"
        >
          <a-input
            v-model:value="formState.fileName"
            placeholder="请输入文件名称"
            size="large"
            show-count
            :maxlength="100"
          />
        </a-form-item>

        <!-- 开启状态 -->
        <a-form-item
          label="开启状态"
          name="isEnabled"
          :rules="rules.isEnabled"
        >
          <a-switch
            v-model:checked="formState.isEnabled"
            checked-children="开启"
            un-checked-children="关闭"
          />
          <span class="switch-description">
            {{ formState.isEnabled ? '当前状态：开启' : '当前状态：关闭' }}
          </span>
        </a-form-item>

        <!-- 标签 -->
        <a-form-item
          label="标签"
          name="tags"
          :rules="rules.tags"
        >
          <div class="tags-container">
            <!-- 现有标签 -->
            <a-tag
              v-for="(tag, index) in formState.tags"
              :key="index"
              closable
              @close="removeTag(index)"
              class="custom-tag"
            >
              {{ tag }}
            </a-tag>

            <!-- 新标签输入框 -->
            <a-input
              v-if="newTagVisible"
              v-model:value="newTagValue"
              @pressEnter="addNewTag"
              @blur="addNewTag"
              class="tag-input"
              placeholder="输入标签名"
              size="small"
            />

            <!-- 新增标签按钮 -->
            <a-tag
              v-else
              @click="showNewTagInput"
              class="add-tag"
            >
              <PlusOutlined />
              新标签
            </a-tag>
          </div>
        </a-form-item>

        <!-- 操作按钮 -->
        <a-form-item class="form-actions">
          <a-space>
            <a-button type="primary" html-type="submit" size="large">
              提交
            </a-button>
            <a-button @click="resetForm" size="large">
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formState = reactive({
  fileName: '政府采购框架协议采购方式管理暂行办法',
  isEnabled: true,
  tags: ['标签1', '标签2', '标签3']
})

// 表单验证规则
const rules = {
  fileName: [
    { required: true, message: '请输入文件名称', trigger: 'blur' },
    { min: 2, max: 100, message: '文件名称长度应在2-100个字符之间', trigger: 'blur' }
  ],
  isEnabled: [
    { required: true, message: '请选择开启状态', trigger: 'change' }
  ],
  tags: [
    { required: true, message: '请至少添加一个标签', trigger: 'change' },
    { type: 'array', min: 1, message: '请至少添加一个标签', trigger: 'change' }
  ]
}

// 标签相关状态
const newTagVisible = ref(false)
const newTagValue = ref('')

// 删除标签
const removeTag = (index: number) => {
  formState.tags.splice(index, 1)
}

// 显示新标签输入框
const showNewTagInput = () => {
  newTagVisible.value = true
}

// 添加新标签
const addNewTag = () => {
  if (newTagValue.value && !formState.tags.includes(newTagValue.value)) {
    formState.tags.push(newTagValue.value)
    message.success('标签添加成功')
  } else if (formState.tags.includes(newTagValue.value)) {
    message.warning('标签已存在')
  }
  newTagVisible.value = false
  newTagValue.value = ''
}

// 表单提交
const onFinish = (values: any) => {
  console.log('表单数据:', values)
  message.success('表单提交成功！')
}

// 表单提交失败
const onFinishFailed = (errorInfo: any) => {
  console.log('表单验证失败:', errorInfo)
  message.error('请检查表单信息')
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  formState.tags = []
  message.info('表单已重置')
}

</script>

<style scoped>
.form-container {
  max-width: 800px;
  margin: 40px auto;
  padding: 24px;
}

.form-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.custom-form {
  margin-top: 16px;
}

.switch-description {
  margin-left: 12px;
  color: #666;
  font-size: 14px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  min-height: 32px;
}

.custom-tag {
  margin: 0;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s;
}

.custom-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-input {
  width: 120px;
  margin: 0;
}

.add-tag {
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
  margin: 0;
}

.add-tag:hover {
  border-color: #1890ff;
  color: #1890ff;
  background: #f0f8ff;
}

.form-actions {
  margin-top: 32px;
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-container {
    margin: 20px;
    padding: 16px;
  }

  .tags-container {
    gap: 6px;
  }

  .tag-input {
    width: 100px;
  }
}

/* 表单项间距调整 */
:deep(.ant-form-item) {
  margin-bottom: 24px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
  color: #262626;
}

/* 输入框样式增强 */
:deep(.ant-input) {
  border-radius: 8px;
  transition: all 0.2s;
}

:deep(.ant-input:focus) {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 开关样式增强 */
:deep(.ant-switch) {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 按钮样式增强 */
:deep(.ant-btn) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s;
}

:deep(.ant-btn-primary) {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

:deep(.ant-btn-primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}
</style>
